#!/usr/bin/env python3
"""
Enterprise WiFi Network Management System
Advanced LAN Device Scanner with comprehensive network management capabilities
"""

import socket
import subprocess
import threading
import time
import json
from datetime import datetime, timedelta
import ipaddress
import re
import platform
import sqlite3
import logging
from flask import Flask, render_template, jsonify, request
from functools import wraps
import hashlib
import secrets

app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnterpriseWiFiManager:
    def __init__(self):
        self.devices = {}
        self.scanning = False
        self.last_scan = None
        self.blocked_devices = set()
        self.bandwidth_limits = {}
        self.device_policies = {}
        self.admin_password = None
        self.session_token = None
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database for persistent storage"""
        self.conn = sqlite3.connect('network_management.db', check_same_thread=False)
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS devices (
                ip TEXT PRIMARY KEY,
                mac TEXT,
                hostname TEXT,
                vendor TEXT,
                device_type TEXT,
                first_seen TIMESTAMP,
                last_seen TIMESTAMP,
                is_blocked INTEGER DEFAULT 0,
                bandwidth_limit INTEGER DEFAULT 0,
                data_usage INTEGER DEFAULT 0
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS access_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_ip TEXT,
                device_mac TEXT,
                action TEXT,
                timestamp TIMESTAMP,
                admin_action INTEGER DEFAULT 0
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS network_policies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                policy_name TEXT,
                device_group TEXT,
                bandwidth_limit INTEGER,
                access_schedule TEXT,
                blocked_sites TEXT,
                priority INTEGER DEFAULT 1
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS admin_settings (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        ''')
        
        self.conn.commit()
    
    def authenticate_admin(self, password):
        """Authenticate admin access"""
        if not self.admin_password:
            # First time setup
            self.admin_password = hashlib.sha256(password.encode()).hexdigest()
            self.save_admin_setting('password_hash', self.admin_password)
            
        stored_hash = self.get_admin_setting('password_hash')
        if stored_hash and hashlib.sha256(password.encode()).hexdigest() == stored_hash:
            self.session_token = secrets.token_hex(32)
            return self.session_token
        return None
    
    def save_admin_setting(self, key, value):
        """Save admin setting to database"""
        cursor = self.conn.cursor()
        cursor.execute('REPLACE INTO admin_settings (key, value) VALUES (?, ?)', (key, value))
        self.conn.commit()
    
    def get_admin_setting(self, key):
        """Get admin setting from database"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT value FROM admin_settings WHERE key = ?', (key,))
        result = cursor.fetchone()
        return result[0] if result else None
    
    def log_access_event(self, device_ip, device_mac, action, admin_action=False):
        """Log network access events"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO access_logs (device_ip, device_mac, action, timestamp, admin_action)
            VALUES (?, ?, ?, ?, ?)
        ''', (device_ip, device_mac, action, datetime.now(), int(admin_action)))
        self.conn.commit()
    
    def get_local_ip(self):
        """Get the local IP address"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "***********"
    
    def get_network_range(self):
        """Get the network range to scan"""
        local_ip = self.get_local_ip()
        network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)
        return str(network)
    
    def get_gateway_ip(self):
        """Get the default gateway IP"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['ipconfig'], capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if 'Default Gateway' in line:
                        gateway = line.split(':')[-1].strip()
                        if gateway:
                            return gateway
            else:
                result = subprocess.run(['ip', 'route', 'show', 'default'], capture_output=True, text=True)
                if result.returncode == 0:
                    parts = result.stdout.split()
                    if len(parts) >= 3:
                        return parts[2]
        except:
            pass
        return self.get_local_ip().rsplit('.', 1)[0] + '.1'
    
    # DOS/DDOS Protection Functions
    def block_device_internet(self, device_ip, device_mac=None):
        """Block internet access for a specific device using iptables/Windows firewall"""
        try:
            if platform.system().lower() == "windows":
                # Windows firewall rules
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    f'name=Block_{device_ip}',
                    'dir=out', 'action=block',
                    f'remoteip={device_ip}'
                ], check=True)
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    f'name=Block_In_{device_ip}',
                    'dir=in', 'action=block',
                    f'remoteip={device_ip}'
                ], check=True)
            else:
                # Linux iptables rules
                subprocess.run([
                    'sudo', 'iptables', '-I', 'FORWARD',
                    '-s', device_ip, '-j', 'DROP'
                ], check=True)
                subprocess.run([
                    'sudo', 'iptables', '-I', 'FORWARD',
                    '-d', device_ip, '-j', 'DROP'
                ], check=True)
            
            self.blocked_devices.add(device_ip)
            self.log_access_event(device_ip, device_mac or 'unknown', 'blocked', admin_action=True)
            logger.info(f"Blocked internet access for device {device_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to block device {device_ip}: {e}")
            return False
    
    def unblock_device_internet(self, device_ip, device_mac=None):
        """Unblock internet access for a specific device"""
        try:
            if platform.system().lower() == "windows":
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                    f'name=Block_{device_ip}'
                ], check=False)
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                    f'name=Block_In_{device_ip}'
                ], check=False)
            else:
                subprocess.run([
                    'sudo', 'iptables', '-D', 'FORWARD',
                    '-s', device_ip, '-j', 'DROP'
                ], check=False)
                subprocess.run([
                    'sudo', 'iptables', '-D', 'FORWARD',
                    '-d', device_ip, '-j', 'DROP'
                ], check=False)
            
            self.blocked_devices.discard(device_ip)
            self.log_access_event(device_ip, device_mac or 'unknown', 'unblocked', admin_action=True)
            logger.info(f"Unblocked internet access for device {device_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to unblock device {device_ip}: {e}")
            return False
    
    def set_bandwidth_limit(self, device_ip, limit_kbps, device_mac=None):
        """Set bandwidth limit for a device using tc (Linux) or netsh (Windows)"""
        try:
            if platform.system().lower() == "linux":
                # Linux traffic control
                interface = self.get_network_interface()
                subprocess.run([
                    'sudo', 'tc', 'qdisc', 'add', 'dev', interface,
                    'root', 'handle', '1:', 'htb', 'default', '30'
                ], check=False)
                
                class_id = str(hash(device_ip) % 1000 + 1)
                subprocess.run([
                    'sudo', 'tc', 'class', 'add', 'dev', interface,
                    'parent', '1:', 'classid', f'1:{class_id}',
                    'htb', 'rate', f'{limit_kbps}kbit'
                ], check=True)
                
                subprocess.run([
                    'sudo', 'tc', 'filter', 'add', 'dev', interface,
                    'protocol', 'ip', 'parent', '1:0',
                    'prio', '1', 'u32', 'match', 'ip', 'dst', device_ip,
                    'flowid', f'1:{class_id}'
                ], check=True)
            
            self.bandwidth_limits[device_ip] = limit_kbps
            self.log_access_event(device_ip, device_mac or 'unknown', f'bandwidth_limited_{limit_kbps}kbps', admin_action=True)
            logger.info(f"Set bandwidth limit {limit_kbps}kbps for device {device_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to set bandwidth limit for {device_ip}: {e}")
            return False
    
    def remove_bandwidth_limit(self, device_ip, device_mac=None):
        """Remove bandwidth limit for a device"""
        try:
            if platform.system().lower() == "linux":
                interface = self.get_network_interface()
                class_id = str(hash(device_ip) % 1000 + 1)
                subprocess.run([
                    'sudo', 'tc', 'class', 'del', 'dev', interface,
                    'classid', f'1:{class_id}'
                ], check=False)
                subprocess.run([
                    'sudo', 'tc', 'filter', 'del', 'dev', interface,
                    'protocol', 'ip', 'parent', '1:0',
                    'prio', '1', 'u32', 'match', 'ip', 'dst', device_ip
                ], check=False)
            
            self.bandwidth_limits.pop(device_ip, None)
            self.log_access_event(device_ip, device_mac or 'unknown', 'bandwidth_limit_removed', admin_action=True)
            logger.info(f"Removed bandwidth limit for device {device_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to remove bandwidth limit for {device_ip}: {e}")
            return False
    
    def get_network_interface(self):
        """Get the primary network interface"""
        try:
            result = subprocess.run(['ip', 'route', 'get', '*******'], capture_output=True, text=True)
            for line in result.stdout.split('\n'):
                if 'dev' in line:
                    parts = line.split()
                    dev_index = parts.index('dev')
                    if dev_index + 1 < len(parts):
                        return parts[dev_index + 1]
        except:
            pass
        return 'eth0'
    
    def kick_device_from_network(self, device_ip, device_mac):
        """Disconnect device from network using ARP spoofing technique"""
        try:
            gateway_ip = self.get_gateway_ip()
            
            # Send ARP replies to confuse the device
            for _ in range(5):
                if platform.system().lower() == "linux":
                    subprocess.run([
                        'sudo', 'arping', '-c', '1', '-A', '-I', 
                        self.get_network_interface(), device_ip
                    ], check=False)
                    time.sleep(0.1)
            
            self.log_access_event(device_ip, device_mac, 'kicked_from_network', admin_action=True)
            logger.info(f"Kicked device {device_ip} from network")
            return True
        except Exception as e:
            logger.error(f"Failed to kick device {device_ip}: {e}")
            return False
    
    def monitor_device_traffic(self, device_ip, duration_seconds=60):
        """Monitor traffic for a specific device"""
        try:
            traffic_data = {
                'device_ip': device_ip,
                'start_time': datetime.now(),
                'bytes_in': 0,
                'bytes_out': 0,
                'packets_in': 0,
                'packets_out': 0,
                'connections': []
            }
            
            if platform.system().lower() == "linux":
                # Use netstat and iftop-like functionality
                result = subprocess.run([
                    'sudo', 'netstat', '-i'
                ], capture_output=True, text=True, timeout=duration_seconds)
                
                # Parse network statistics (simplified)
                traffic_data['monitoring_complete'] = True
            
            return traffic_data
        except Exception as e:
            logger.error(f"Failed to monitor traffic for {device_ip}: {e}")
            return None
    
    def perform_ddos_mitigation(self, attacker_ip):
        """Implement DDoS mitigation for detected attacking IP"""
        try:
            # Block the attacker immediately
            self.block_device_internet(attacker_ip)
            
            # Add rate limiting rules
            if platform.system().lower() == "linux":
                subprocess.run([
                    'sudo', 'iptables', '-I', 'INPUT',
                    '-s', attacker_ip,
                    '-m', 'limit', '--limit', '1/s', '--limit-burst', '1',
                    '-j', 'ACCEPT'
                ], check=True)
                
                subprocess.run([
                    'sudo', 'iptables', '-I', 'INPUT',
                    '-s', attacker_ip, '-j', 'DROP'
                ], check=True)
            
            self.log_access_event(attacker_ip, 'unknown', 'ddos_mitigation_applied', admin_action=True)
            logger.warning(f"Applied DDoS mitigation for IP {attacker_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to apply DDoS mitigation for {attacker_ip}: {e}")
            return False
    
    def detect_suspicious_activity(self):
        """Detect suspicious network activity patterns"""
        suspicious_devices = []
        
        try:
            # Check for devices with excessive connection attempts
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT device_ip, COUNT(*) as attempt_count
                FROM access_logs
                WHERE timestamp > datetime('now', '-5 minutes')
                AND action LIKE '%connection%'
                GROUP BY device_ip
                HAVING attempt_count > 50
            ''')
            
            for row in cursor.fetchall():
                suspicious_devices.append({
                    'ip': row[0],
                    'reason': 'excessive_connections',
                    'count': row[1]
                })
            
            return suspicious_devices
        except Exception as e:
            logger.error(f"Error detecting suspicious activity: {e}")
            return []
    
    def create_device_profile(self, device_ip, device_info):
        """Create or update device profile in database"""
        cursor = self.conn.cursor()
        cursor.execute('''
            REPLACE INTO devices (
                ip, mac, hostname, vendor, device_type, 
                first_seen, last_seen, is_blocked, bandwidth_limit
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            device_ip,
            device_info.get('mac', ''),
            device_info.get('hostname', ''),
            device_info.get('vendor', 'Unknown'),
            device_info.get('device_type', 'Unknown'),
            datetime.now(),
            datetime.now(),
            int(device_ip in self.blocked_devices),
            self.bandwidth_limits.get(device_ip, 0)
        ))
        self.conn.commit()
    
    def get_network_statistics(self):
        """Get comprehensive network statistics"""
        stats = {
            'total_devices': len(self.devices),
            'online_devices': len([d for d in self.devices.values() if d['status'] == 'online']),
            'blocked_devices': len(self.blocked_devices),
            'bandwidth_limited_devices': len(self.bandwidth_limits),
            'last_scan': self.last_scan,
            'scanning': self.scanning
        }
        
        # Get device type breakdown
        device_types = {}
        for device in self.devices.values():
            dtype = device.get('device_type', 'Unknown')
            device_types[dtype] = device_types.get(dtype, 0) + 1
        
        stats['device_types'] = device_types
        return stats
    
    # Original scanning functions (enhanced)
    def get_device_icon(self, device_info):
        """Determine device icon based on device information"""
        hostname = device_info.get('hostname', '').lower()
        
        if any(keyword in hostname for keyword in ['router', 'gateway', 'modem', 'fritz', 'speedport']):
            return '🌐'
        if any(keyword in hostname for keyword in ['iphone', 'android', 'mobile', 'phone', 'samsung', 'huawei']):
            return '📱'
        if any(keyword in hostname for keyword in ['pc', 'desktop', 'laptop', 'macbook', 'imac', 'windows', 'ubuntu']):
            return '💻'
        if any(keyword in hostname for keyword in ['tv', 'roku', 'chromecast', 'firestick', 'appletv', 'smart']):
            return '📺'
        if any(keyword in hostname for keyword in ['alexa', 'echo', 'nest', 'iot', 'sensor']):
            return '🏠'
        if any(keyword in hostname for keyword in ['printer', 'print', 'canon', 'epson', 'hp']):
            return '🖨️'
        if any(keyword in hostname for keyword in ['xbox', 'playstation', 'nintendo', 'switch']):
            return '🎮'
        if any(keyword in hostname for keyword in ['raspberry', 'pi', 'raspberrypi']):
            return '🥧'
        return '🔧'
    
    def ping_host(self, ip):
        """Ping a host to check if it's alive"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                      capture_output=True, text=True, timeout=3)
                return result.returncode == 0
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                      capture_output=True, text=True, timeout=3)
                return result.returncode == 0
        except:
            return False
    
    def get_hostname(self, ip):
        """Get hostname for an IP address"""
        try:
            hostname = socket.gethostbyaddr(ip)[0]
            return hostname
        except:
            return f"Device-{ip.split('.')[-1]}"
    
    def get_mac_address(self, ip):
        """Get MAC address using ARP table"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['arp', '-a', ip], capture_output=True, text=True, timeout=3)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if ip in line:
                            mac_match = re.search(r'([0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}', line)
                            if mac_match:
                                return mac_match.group(0).replace('-', ':').lower()
            else:
                result = subprocess.run(['arp', '-n', ip], capture_output=True, text=True, timeout=3)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if ip in line:
                            parts = line.split()
                            if len(parts) >= 3:
                                return parts[2].lower()
        except:
            pass
        return ""
    
    def check_common_ports(self, ip):
        """Check common ports on a device"""
        common_ports = [22, 23, 53, 80, 135, 139, 443, 445, 993, 995, 8080]
        open_ports = []
        
        for port in common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((ip, port))
                if result == 0:
                    service = self.get_service_name(port)
                    open_ports.append({'port': port, 'service': service})
                sock.close()
            except:
                pass
        
        return open_ports
    
    def get_service_name(self, port):
        """Get service name for a port"""
        services = {
            22: 'SSH', 23: 'Telnet', 53: 'DNS', 80: 'HTTP', 135: 'RPC',
            139: 'NetBIOS', 443: 'HTTPS', 445: 'SMB', 993: 'IMAPS', 
            995: 'POP3S', 8080: 'HTTP-Alt'
        }
        return services.get(port, 'Unknown')
    
    def scan_device(self, ip):
        """Scan a single device with enhanced information"""
        if not self.ping_host(ip):
            return None
            
        device_info = {
            'ip': ip,
            'hostname': self.get_hostname(ip),
            'mac': self.get_mac_address(ip),
            'vendor': 'Unknown',
            'ports': self.check_common_ports(ip),
            'status': 'online',
            'last_seen': datetime.now().isoformat(),
            'is_blocked': ip in self.blocked_devices,
            'bandwidth_limit': self.bandwidth_limits.get(ip, 0),
            'device_type': self.classify_device_type(ip)
        }
        
        device_info['icon'] = self.get_device_icon(device_info)
        self.create_device_profile(ip, device_info)
        return device_info
    
    def classify_device_type(self, ip):
        """Classify device type based on open ports and behavior"""
        ports = self.check_common_ports(ip)
        port_numbers = [p['port'] for p in ports]
        
        if 80 in port_numbers or 443 in port_numbers:
            return 'Web Server'
        elif 22 in port_numbers:
            return 'Linux/Unix Server'
        elif 135 in port_numbers and 445 in port_numbers:
            return 'Windows Computer'
        elif any(p in port_numbers for p in [993, 995]):
            return 'Email Server'
        else:
            return 'Unknown Device'
    
    def scan_network(self):
        """Scan the entire network with enhanced logging"""
        if self.scanning:
            return
            
        self.scanning = True
        logger.info("🔍 Starting comprehensive network scan...")
        
        network_range = self.get_network_range()
        logger.info(f"📡 Scanning network: {network_range}")
        
        self.devices = {}
        network = ipaddress.IPv4Network(network_range)
        
        threads = []
        for ip in network.hosts():
            ip_str = str(ip)
            thread = threading.Thread(target=self._scan_and_store, args=(ip_str,))
            threads.append(thread)
            thread.start()
            
            if len(threads) >= 50:
                for t in threads:
                    t.join()
                threads = []
        
        for thread in threads:
            thread.join()
        
        # Check for suspicious activity after scan
        suspicious = self.detect_suspicious_activity()
        if suspicious:
            logger.warning(f"Detected {len(suspicious)} suspicious devices")
        
        self.last_scan = datetime.now()
        self.scanning = False
        online_count = len([d for d in self.devices.values() if d['status'] == 'online'])
        logger.info(f"✅ Scan complete! Found {online_count} online devices")
    
    def _scan_and_store(self, ip):
        """Helper method to scan and store device info"""
        device_info = self.scan_device(ip)
        if device_info:
            self.devices[ip] = device_info

# Global manager instance
manager = EnterpriseWiFiManager()

def require_auth(f):
    """Decorator for admin authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token or token != manager.session_token:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

# Web API Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    password = data.get('password')
    token = manager.authenticate_admin(password)
    if token:
        return jsonify({'success': True, 'token': token})
    return jsonify({'success': False, 'error': 'Invalid password'}), 401

@app.route('/api/devices')
def get_devices():
    online_devices = {ip: info for ip, info in manager.devices.items() if info['status'] == 'online'}
    return jsonify({
        'devices': online_devices,
        'total_devices': len(online_devices),
        'last_scan': manager.last_scan.isoformat() if manager.last_scan else None,
        'scanning': manager.scanning,
        'blocked_devices': list(manager.blocked_devices),
        'bandwidth_limits': manager.bandwidth_limits
    })

@app.route('/api/scan')
@require_auth
def start_scan():
    if not manager.scanning:
        thread = threading.Thread(target=manager.scan_network)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'scan_started'})
    return jsonify({'status': 'already_scanning'})

@app.route('/api/block-device', methods=['POST'])
@require_auth
def block_device():
    data = request.get_json()
    device_ip = data.get('ip')
    device_mac = data.get('mac')
    
    if manager.block_device_internet(device_ip, device_mac):
        return jsonify({'success': True, 'message': f'Device {device_ip} blocked'})
    return jsonify({'success': False, 'error': 'Failed to block device'}), 500

@app.route('/api/unblock-device', methods=['POST'])
@require_auth
def unblock_device():
    data = request.get_json()
    device_ip = data.get('ip')
    device_mac = data.get('mac')
    
    if manager.unblock_device_internet(device_ip, device_mac):
        return jsonify({'success': True, 'message': f'Device {device_ip} unblocked'})
    return jsonify({'success': False, 'error': 'Failed to unblock device'}), 500

@app.route('/api/set-bandwidth', methods=['POST'])
@require_auth
def set_bandwidth():
    data = request.get_json()
    device_ip = data.get('ip')
    limit_kbps = data.get('limit_kbps')
    device_mac = data.get('mac')
    
    if manager.set_bandwidth_limit(device_ip, limit_kbps, device_mac):
        return jsonify({'success': True, 'message': f'Bandwidth limit set for {device_ip}'})
    return jsonify({'success': False, 'error': 'Failed to set bandwidth limit'}), 500

@app.route('/api/remove-bandwidth', methods=['POST'])
@require_auth
def remove_bandwidth():
    data = request.get_json()
    device_ip = data.get('ip')
    device_mac = data.get('mac')
    
    if manager.remove_bandwidth_limit(device_ip, device_mac):
        return jsonify({'success': True, 'message': f'Bandwidth limit removed for {device_ip}'})
    return jsonify({'success': False, 'error': 'Failed to remove bandwidth limit'}), 500

@app.route('/api/kick-device', methods=['POST'])
@require_auth
def kick_device():
    data = request.get_json()
    device_ip = data.get('ip')
    device_mac = data.get('mac')
    
    if manager.kick_device_from_network(device_ip, device_mac):
        return jsonify({'success': True, 'message': f'Device {device_ip} kicked from network'})
    return jsonify({'success': False, 'error': 'Failed to kick device'}), 500

@app.route('/api/monitor-traffic/<device_ip>')
@require_auth
def monitor_traffic(device_ip):
    duration = request.args.get('duration', 60, type=int)
    traffic_data = manager.monitor_device_traffic(device_ip, duration)
    return jsonify(traffic_data or {'error': 'Failed to monitor traffic'})

@app.route('/api/ddos-mitigation', methods=['POST'])
@require_auth
def apply_ddos_mitigation():
    data = request.get_json()
    attacker_ip = data.get('ip')
    
    if manager.perform_ddos_mitigation(attacker_ip):
        return jsonify({'success': True, 'message': f'DDoS mitigation applied for {attacker_ip}'})
    return jsonify({'success': False, 'error': 'Failed to apply DDoS mitigation'}), 500

@app.route('/api/suspicious-activity')
@require_auth
def get_suspicious_activity():
    suspicious_devices = manager.detect_suspicious_activity()
    return jsonify({'suspicious_devices': suspicious_devices})

@app.route('/api/network-stats')
def get_network_stats():
    stats = manager.get_network_statistics()
    return jsonify(stats)

@app.route('/api/access-logs')
@require_auth
def get_access_logs():
    limit = request.args.get('limit', 100, type=int)
    cursor = manager.conn.cursor()
    cursor.execute('''
        SELECT device_ip, device_mac, action, timestamp, admin_action
        FROM access_logs
        ORDER BY timestamp DESC
        LIMIT ?
    ''', (limit,))
    
    logs = []
    for row in cursor.fetchall():
        logs.append({
            'device_ip': row[0],
            'device_mac': row[1],
            'action': row[2],
            'timestamp': row[3],
            'admin_action': bool(row[4])
        })
    
    return jsonify({'logs': logs})

@app.route('/api/network-info')
def get_network_info():
    local_ip = manager.get_local_ip()
    network_range = manager.get_network_range()
    gateway_ip = manager.get_gateway_ip()
    
    return jsonify({
        'local_ip': local_ip,
        'network_range': network_range,
        'gateway_ip': gateway_ip,
        'interface': manager.get_network_interface()
    })

@app.route('/api/bulk-action', methods=['POST'])
@require_auth
def bulk_action():
    """Perform bulk actions on multiple devices"""
    data = request.get_json()
    action = data.get('action')
    device_ips = data.get('device_ips', [])
    
    results = []
    for ip in device_ips:
        device_mac = manager.devices.get(ip, {}).get('mac', 'unknown')
        success = False
        
        if action == 'block':
            success = manager.block_device_internet(ip, device_mac)
        elif action == 'unblock':
            success = manager.unblock_device_internet(ip, device_mac)
        elif action == 'kick':
            success = manager.kick_device_from_network(ip, device_mac)
        elif action == 'set_bandwidth':
            limit = data.get('bandwidth_limit', 1000)
            success = manager.set_bandwidth_limit(ip, limit, device_mac)
        
        results.append({'ip': ip, 'success': success})
    
    return jsonify({'results': results})

@app.route('/api/device-groups', methods=['GET', 'POST'])
@require_auth
def device_groups():
    """Manage device groups for bulk operations"""
    if request.method == 'POST':
        data = request.get_json()
        group_name = data.get('group_name')
        device_ips = data.get('device_ips', [])
        
        # Save device group to database
        cursor = manager.conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO network_policies 
            (policy_name, device_group, priority) 
            VALUES (?, ?, ?)
        ''', (group_name, json.dumps(device_ips), 1))
        manager.conn.commit()
        
        return jsonify({'success': True, 'message': f'Device group {group_name} created'})
    
    else:
        # Get all device groups
        cursor = manager.conn.cursor()
        cursor.execute('SELECT policy_name, device_group FROM network_policies')
        groups = []
        for row in cursor.fetchall():
            groups.append({
                'name': row[0],
                'devices': json.loads(row[1]) if row[1] else []
            })
        return jsonify({'groups': groups})

@app.route('/api/whitelist', methods=['GET', 'POST', 'DELETE'])
@require_auth
def manage_whitelist():
    """Manage device whitelist"""
    if request.method == 'POST':
        data = request.get_json()
        device_ip = data.get('ip')
        device_mac = data.get('mac')
        
        # Add to whitelist (remove any blocks and restrictions)
        manager.unblock_device_internet(device_ip, device_mac)
        manager.remove_bandwidth_limit(device_ip, device_mac)
        
        # Save to database
        manager.save_admin_setting(f'whitelist_{device_ip}', 'true')
        manager.log_access_event(device_ip, device_mac, 'added_to_whitelist', admin_action=True)
        
        return jsonify({'success': True, 'message': f'Device {device_ip} added to whitelist'})
    
    elif request.method == 'DELETE':
        data = request.get_json()
        device_ip = data.get('ip')
        
        # Remove from whitelist
        cursor = manager.conn.cursor()
        cursor.execute('DELETE FROM admin_settings WHERE key = ?', (f'whitelist_{device_ip}',))
        manager.conn.commit()
        
        return jsonify({'success': True, 'message': f'Device {device_ip} removed from whitelist'})
    
    else:
        # Get whitelist
        cursor = manager.conn.cursor()
        cursor.execute('SELECT key FROM admin_settings WHERE key LIKE "whitelist_%"')
        whitelist = [row[0].replace('whitelist_', '') for row in cursor.fetchall()]
        return jsonify({'whitelist': whitelist})

@app.route('/api/network-health')
@require_auth
def network_health():
    """Get comprehensive network health metrics"""
    health_data = {
        'timestamp': datetime.now().isoformat(),
        'total_devices': len(manager.devices),
        'online_devices': len([d for d in manager.devices.values() if d['status'] == 'online']),
        'blocked_devices': len(manager.blocked_devices),
        'bandwidth_limited_devices': len(manager.bandwidth_limits),
        'suspicious_activity_count': len(manager.detect_suspicious_activity()),
        'network_load': 'normal',  # Could be enhanced with actual traffic monitoring
        'gateway_status': 'online' if manager.ping_host(manager.get_gateway_ip()) else 'offline',
        'dns_status': 'online' if manager.ping_host('*******') else 'offline'
    }
    
    # Calculate network utilization (simplified)
    total_bandwidth_allocated = sum(manager.bandwidth_limits.values())
    health_data['bandwidth_utilization'] = min(100, (total_bandwidth_allocated / 10000) * 100)  # Assume 10Mbps total
    
    # Device type distribution
    device_types = {}
    for device in manager.devices.values():
        dtype = device.get('device_type', 'Unknown')
        device_types[dtype] = device_types.get(dtype, 0) + 1
    health_data['device_type_distribution'] = device_types
    
    return jsonify(health_data)

@app.route('/api/export-config', methods=['GET'])
@require_auth
def export_config():
    """Export network configuration"""
    config = {
        'devices': manager.devices,
        'blocked_devices': list(manager.blocked_devices),
        'bandwidth_limits': manager.bandwidth_limits,
        'network_info': {
            'local_ip': manager.get_local_ip(),
            'network_range': manager.get_network_range(),
            'gateway_ip': manager.get_gateway_ip()
        },
        'export_timestamp': datetime.now().isoformat()
    }
    
    return jsonify(config)

@app.route('/api/import-config', methods=['POST'])
@require_auth
def import_config():
    """Import network configuration"""
    try:
        data = request.get_json()
        
        # Import blocked devices
        for device_ip in data.get('blocked_devices', []):
            manager.block_device_internet(device_ip)
        
        # Import bandwidth limits
        for device_ip, limit in data.get('bandwidth_limits', {}).items():
            manager.set_bandwidth_limit(device_ip, limit)
        
        return jsonify({'success': True, 'message': 'Configuration imported successfully'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/emergency-lockdown', methods=['POST'])
@require_auth
def emergency_lockdown():
    """Emergency network lockdown - block all devices except whitelisted"""
    try:
        # Get whitelist
        cursor = manager.conn.cursor()
        cursor.execute('SELECT key FROM admin_settings WHERE key LIKE "whitelist_%"')
        whitelisted_ips = [row[0].replace('whitelist_', '') for row in cursor.fetchall()]
        
        # Add admin IP to whitelist
        admin_ip = manager.get_local_ip()
        whitelisted_ips.append(admin_ip)
        
        blocked_count = 0
        for device_ip in manager.devices.keys():
            if device_ip not in whitelisted_ips:
                if manager.block_device_internet(device_ip):
                    blocked_count += 1
        
        manager.log_access_event('system', 'system', f'emergency_lockdown_{blocked_count}_devices', admin_action=True)
        
        return jsonify({
            'success': True, 
            'message': f'Emergency lockdown activated. {blocked_count} devices blocked.',
            'blocked_count': blocked_count
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lift-lockdown', methods=['POST'])
@require_auth
def lift_lockdown():
    """Lift emergency lockdown"""
    try:
        unblocked_count = 0
        for device_ip in list(manager.blocked_devices):
            if manager.unblock_device_internet(device_ip):
                unblocked_count += 1
        
        manager.log_access_event('system', 'system', f'lockdown_lifted_{unblocked_count}_devices', admin_action=True)
        
        return jsonify({
            'success': True,
            'message': f'Emergency lockdown lifted. {unblocked_count} devices unblocked.',
            'unblocked_count': unblocked_count
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/schedule-action', methods=['POST'])
@require_auth
def schedule_action():
    """Schedule actions for later execution"""
    data = request.get_json()
    device_ip = data.get('ip')
    action = data.get('action')  # block, unblock, set_bandwidth, etc.
    schedule_time = data.get('schedule_time')  # ISO format datetime
    
    try:
        # Store scheduled action in database
        cursor = manager.conn.cursor()
        cursor.execute('''
            INSERT INTO admin_settings (key, value) 
            VALUES (?, ?)
        ''', (f'scheduled_{int(time.time())}', json.dumps({
            'device_ip': device_ip,
            'action': action,
            'schedule_time': schedule_time,
            'data': data
        })))
        manager.conn.commit()
        
        return jsonify({'success': True, 'message': f'Action scheduled for {schedule_time}'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup-restore', methods=['GET', 'POST'])
@require_auth
def backup_restore():
    """Backup and restore network database"""
    if request.method == 'POST':
        # Restore from backup
        try:
            backup_data = request.get_json()
            
            # Clear existing data
            cursor = manager.conn.cursor()
            cursor.execute('DELETE FROM devices')
            cursor.execute('DELETE FROM access_logs')
            cursor.execute('DELETE FROM network_policies')
            
            # Restore data
            for device_data in backup_data.get('devices', []):
                cursor.execute('''
                    INSERT INTO devices (
                        ip, mac, hostname, vendor, device_type, 
                        first_seen, last_seen, is_blocked, bandwidth_limit
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', device_data)
            
            manager.conn.commit()
            return jsonify({'success': True, 'message': 'Backup restored successfully'})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500
    
    else:
        # Create backup
        cursor = manager.conn.cursor()
        cursor.execute('SELECT * FROM devices')
        devices_backup = cursor.fetchall()
        
        cursor.execute('SELECT * FROM access_logs')
        logs_backup = cursor.fetchall()
        
        cursor.execute('SELECT * FROM network_policies')
        policies_backup = cursor.fetchall()
        
        backup_data = {
            'devices': devices_backup,
            'access_logs': logs_backup,
            'network_policies': policies_backup,
            'backup_timestamp': datetime.now().isoformat()
        }
        
        return jsonify(backup_data)

if __name__ == '__main__':
    print("🚀 Starting Enterprise WiFi Management System...")
    print("📡 Advanced network device management with security features")
    print("🔐 Admin authentication required for management functions")
    print("🌐 Web interface available at: http://localhost:5000")
    print("💼 Enterprise features: DoS protection, bandwidth control, device blocking")
    
    # Show network info
    local_ip = manager.get_local_ip()
    network_range = manager.get_network_range()
    gateway_ip = manager.get_gateway_ip()
    
    print(f"🏠 Your IP: {local_ip}")
    print(f"📊 Scanning range: {network_range}")
    print(f"🌐 Gateway: {gateway_ip}")
    print("⚠️  Note: Some features require sudo/admin privileges")
    
    # Start initial scan in background
    initial_scan_thread = threading.Thread(target=manager.scan_network)
    initial_scan_thread.daemon = True
    initial_scan_thread.start()
    
    # Start web server
    app.run(host='0.0.0.0', port=5000, debug=False)