<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise WiFi Management System</title>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --warning: 38 92% 50%;
            --warning-foreground: 48 96% 89%;
            --success: 142 76% 36%;
            --success-foreground: 355 100% 97%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header h1 {
            font-size: 2.25rem;
            font-weight: 700;
            color: hsl(var(--foreground));
            letter-spacing: -0.025em;
        }

        .header-controls {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .auth-status {
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            background-color: hsl(var(--muted));
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .auth-status.authenticated {
            background-color: hsl(var(--success));
            color: hsl(var(--success-foreground));
        }

        .tabs {
            display: flex;
            gap: 0.25rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid hsl(var(--border));
        }

        .tab {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            color: hsl(var(--muted-foreground));
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab.active {
            color: hsl(var(--foreground));
            border-bottom-color: hsl(var(--primary));
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .controls {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border-radius: calc(var(--radius) - 2px);
            font-size: 0.875rem;
            font-weight: 500;
            height: 2.5rem;
            padding: 0 1rem;
            border: 1px solid hsl(var(--border));
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }

        .btn:hover {
            background-color: hsl(var(--accent));
            color: hsl(var(--accent-foreground));
        }

        .btn-primary {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            border-color: hsl(var(--primary));
        }

        .btn-primary:hover {
            background-color: hsl(var(--primary) / 0.9);
        }

        .btn-destructive {
            background-color: hsl(var(--destructive));
            color: hsl(var(--destructive-foreground));
            border-color: hsl(var(--destructive));
        }

        .btn-destructive:hover {
            background-color: hsl(var(--destructive) / 0.9);
        }

        .btn-warning {
            background-color: hsl(var(--warning));
            color: hsl(var(--warning-foreground));
            border-color: hsl(var(--warning));
        }

        .btn-success {
            background-color: hsl(var(--success));
            color: hsl(var(--success-foreground));
            border-color: hsl(var(--success));
        }

        .btn:disabled {
            pointer-events: none;
            opacity: 0.5;
        }

        .card {
            background-color: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid hsl(var(--border));
        }

        .card-content {
            padding: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            padding: 1.5rem;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: hsl(var(--foreground));
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .device-card {
            transition: box-shadow 0.2s ease-in-out;
            position: relative;
        }

        .device-card:hover {
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        .device-card.blocked {
            border-color: hsl(var(--destructive));
            background-color: hsl(var(--destructive) / 0.05);
        }

        .device-card.limited {
            border-color: hsl(var(--warning));
            background-color: hsl(var(--warning) / 0.05);
        }

        .device-header {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .device-icon {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3rem;
            height: 3rem;
            background-color: hsl(var(--muted));
            border-radius: var(--radius);
        }

        .device-title h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: hsl(var(--card-foreground));
            margin-bottom: 0.25rem;
        }

        .device-ip {
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, monospace;
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .device-status {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .device-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid hsl(var(--border));
        }

        .device-actions .btn {
            font-size: 0.75rem;
            height: 2rem;
            padding: 0 0.75rem;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            border-radius: calc(var(--radius) - 2px);
            padding: 0.125rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
        }

        .badge-destructive {
            background-color: hsl(var(--destructive));
            color: hsl(var(--destructive-foreground));
        }

        .badge-warning {
            background-color: hsl(var(--warning));
            color: hsl(var(--warning-foreground));
        }

        .badge-success {
            background-color: hsl(var(--success));
            color: hsl(var(--success-foreground));
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: hsl(var(--card));
            border-radius: var(--radius);
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: hsl(var(--muted-foreground));
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: hsl(var(--foreground));
        }

        .form-input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid hsl(var(--border));
            border-radius: calc(var(--radius) - 2px);
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .form-input:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .alert {
            padding: 1rem;
            border-radius: var(--radius);
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: hsl(var(--success) / 0.1);
            border: 1px solid hsl(var(--success));
            color: hsl(var(--success));
        }

        .alert-error {
            background-color: hsl(var(--destructive) / 0.1);
            border: 1px solid hsl(var(--destructive));
            color: hsl(var(--destructive));
        }

        .alert-warning {
            background-color: hsl(var(--warning) / 0.1);
            border: 1px solid hsl(var(--warning));
            color: hsl(var(--warning));
        }

        .logs-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .log-entry {
            padding: 0.75rem;
            border-bottom: 1px solid hsl(var(--border));
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-entry.admin-action {
            background-color: hsl(var(--primary) / 0.05);
        }

        .emergency-panel {
            background-color: hsl(var(--destructive) / 0.1);
            border: 2px solid hsl(var(--destructive));
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .emergency-title {
            color: hsl(var(--destructive));
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .theme-toggle {
            position: fixed;
            top: 2rem;
            right: 2rem;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 2rem;
        }

        .spinner {
            width: 1.5rem;
            height: 1.5rem;
            border: 2px solid hsl(var(--border));
            border-top: 2px solid hsl(var(--foreground));
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .devices-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                align-items: flex-start;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .device-actions {
                flex-direction: column;
            }

            .device-actions .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <button class="btn theme-toggle" onclick="toggleTheme()" id="theme-toggle">
        🌙
    </button>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Admin Authentication</h2>
            </div>
            <div id="login-error" class="alert alert-error hidden"></div>
            <form id="login-form">
                <div class="form-group">
                    <label class="form-label" for="password">Admin Password</label>
                    <input type="password" id="password" class="form-input" placeholder="Enter admin password" required>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">Login</button>
            </form>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <div>
                <h1>Enterprise WiFi Management</h1>
                <p>Advanced network device management and security</p>
            </div>
            <div class="header-controls">
                <div id="auth-status" class="auth-status">Not Authenticated</div>
                <button class="btn" onclick="showLoginModal()">🔐 Login</button>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('dashboard')">📊 Dashboard</button>
            <button class="tab" onclick="switchTab('devices')">🔧 Devices</button>
            <button class="tab" onclick="switchTab('security')">🛡️ Security</button>
            <button class="tab" onclick="switchTab('logs')">📝 Logs</button>
            <button class="tab" onclick="switchTab('settings')">⚙️ Settings</button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard-tab" class="tab-content active">
            <div class="stats-grid">
                <div class="card stat-card">
                    <div class="stat-value" id="total-devices">0</div>
                    <div class="stat-label">Total Devices</div>
                </div>
                <div class="card stat-card">
                    <div class="stat-value" id="blocked-devices">0</div>
                    <div class="stat-label">Blocked Devices</div>
                </div>
                <div class="card stat-card">
                    <div class="stat-value" id="limited-devices">0</div>
                    <div class="stat-label">Bandwidth Limited</div>
                </div>
                <div class="card stat-card">
                    <div class="stat-value" id="suspicious-devices">0</div>
                    <div class="stat-label">Suspicious Activity</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Network Health</h3>
                </div>
                <div class="card-content">
                    <div id="network-health"></div>
                </div>
            </div>
        </div>

        <!-- Devices Tab -->
        <div id="devices-tab" class="tab-content">
            <div class="controls">
                <button class="btn btn-primary" onclick="startScan()" id="scanBtn">
                    🔍 Scan Network
                </button>
                <button class="btn" onclick="refreshDevices()">
                    🔄 Refresh
                </button>
                <button class="btn btn-warning" onclick="showBulkActions()">
                    📦 Bulk Actions
                </button>
                <button class="btn" onclick="exportConfig()">
                    💾 Export Config
                </button>
            </div>

            <div id="status-message" class="card">
                <div class="card-content">
                    <p id="status">Ready to scan your network...</p>
                </div>
            </div>

            <div id="devices-loading" class="card hidden">
                <div class="loading">
                    <div class="spinner"></div>
                    <span>Scanning network...</span>
                </div>
            </div>

            <div id="devices-container"></div>
        </div>

        <!-- Security Tab -->
        <div id="security-tab" class="tab-content">
            <div class="emergency-panel">
                <h3 class="emergency-title">🚨 Emergency Controls</h3>
                <div class="controls">
                    <button class="btn btn-destructive" onclick="emergencyLockdown()">
                        🔒 Emergency Lockdown
                    </button>
                    <button class="btn btn-success" onclick="liftLockdown()">
                        🔓 Lift Lockdown
                    </button>
                </div>
                <p style="margin-top: 1rem; color: hsl(var(--muted-foreground)); font-size: 0.875rem;">
                    Emergency lockdown will block all devices except whitelisted ones.
                </p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Suspicious Activity</h3>
                    <button class="btn" onclick="refreshSuspiciousActivity()">🔄 Refresh</button>
                </div>
                <div class="card-content">
                    <div id="suspicious-activity"></div>
                </div>
            </div>

            <div class="card" style="margin-top: 1.5rem;">
                <div class="card-header">
                    <h3>Whitelist Management</h3>
                    <button class="btn" onclick="refreshWhitelist()">🔄 Refresh</button>
                </div>
                <div class="card-content">
                    <div id="whitelist-container"></div>
                </div>
            </div>
        </div>

        <!-- Logs Tab -->
        <div id="logs-tab" class="tab-content">
            <div class="controls">
                <button class="btn" onclick="refreshLogs()">🔄 Refresh Logs</button>
                <button class="btn" onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Access Logs</h3>
                </div>
                <div class="card-content">
                    <div id="logs-container" class="logs-container"></div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>Network Information</h3>
                </div>
                <div class="card-content">
                    <div id="network-info"></div>
                </div>
            </div>

            <div class="card" style="margin-top: 1.5rem;">
                <div class="card-header">
                    <h3>Configuration</h3>
                </div>
                <div class="card-content">
                    <div class="controls">
                        <button class="btn" onclick="exportConfig()">💾 Export Config</button>
                        <input type="file" id="config-file" accept=".json" style="display: none;" onchange="importConfig()">
                        <button class="btn" onclick="document.getElementById('config-file').click()">📁 Import Config</button>
                        <button class="btn" onclick="backupDatabase()">🗄️ Backup Database</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Action Modal -->
    <div id="action-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="action-modal-title">Device Action</h2>
                <button class="close-btn" onclick="hideModal('action-modal')">&times;</button>
            </div>
            <div id="action-modal-body"></div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulk-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Bulk Actions</h2>
                <button class="close-btn" onclick="hideModal('bulk-modal')">&times;</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">Select Action</label>
                    <select id="bulk-action" class="form-input">
                        <option value="block">Block Internet Access</option>
                        <option value="unblock">Unblock Internet Access</option>
                        <option value="kick">Kick from Network</option>
                        <option value="set_bandwidth">Set Bandwidth Limit</option>
                    </select>
                </div>
                <div class="form-group" id="bandwidth-input" style="display: none;">
                    <label class="form-label">Bandwidth Limit (kbps)</label>
                    <input type="number" id="bulk-bandwidth" class="form-input" placeholder="1000">
                </div>
                <div class="form-group">
                    <label class="form-label">Select Devices</label>
                    <div id="device-selection"></div>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="executeBulkAction()">Execute Action</button>
                    <button class="btn" onclick="hideModal('bulk-modal')">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let authToken = null;
        let currentDevices = {};
        let scanInterval = null;
        let isDarkMode = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            switchTab('dashboard');
        });

        // Authentication
        function showLoginModal() {
            document.getElementById('login-modal').style.display = 'flex';
        }

        function hideLoginModal() {
            document.getElementById('login-modal').style.display = 'none';
        }

        function checkAuthStatus() {
            if (!authToken) {
                showLoginModal();
            } else {
                updateAuthStatus(true);
                refreshAllData();
            }
        }

        function updateAuthStatus(authenticated) {
            const statusEl = document.getElementById('auth-status');
            if (authenticated) {
                statusEl.textContent = '✅ Authenticated';
                statusEl.classList.add('authenticated');
                hideLoginModal();
            } else {
                statusEl.textContent = '❌ Not Authenticated';
                statusEl.classList.remove('authenticated');
                showLoginModal();
            }
        }

        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({password})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    authToken = result.token;
                    updateAuthStatus(true);
                    document.getElementById('password').value = '';
                    refreshAllData();
                } else {
                    showError('login-error', result.error);
                }
            } catch (error) {
                showError('login-error', 'Login failed. Please try again.');
            }
        });

        // Tab Management
        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
            
            if (tabName === 'dashboard') refreshDashboard();
            if (tabName === 'devices') refreshDevices();
            if (tabName === 'security') refreshSecurity();
            if (tabName === 'logs') refreshLogs();
            if (tabName === 'settings') refreshSettings();
        }

        // API Helper
        async function apiRequest(url, options = {}) {
            if (!authToken && !url.includes('/api/login') && !url.includes('/api/devices') && !url.includes('/api/network-info')) {
                showError('status', 'Authentication required');
                return null;
            }
            
            const headers = {'Content-Type': 'application/json'};
            if (authToken) {
                headers['Authorization'] = authToken;
            }
            
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {...headers, ...options.headers}
                });
                
                if (response.status === 401) {
                    authToken = null;
                    updateAuthStatus(false);
                    return null;
                }
                
                return await response.json();
            } catch (error) {
                console.error('API request failed:', error);
                return null;
            }
        }

        // Dashboard Functions
        async function refreshDashboard() {
            const [devicesData, healthData, suspiciousData] = await Promise.all([
                apiRequest('/api/devices'),
                apiRequest('/api/network-health'),
                apiRequest('/api/suspicious-activity')
            ]);
            
            if (devicesData) {
                document.getElementById('total-devices').textContent = devicesData.total_devices || 0;
                document.getElementById('blocked-devices').textContent = devicesData.blocked_devices?.length || 0;
                document.getElementById('limited-devices').textContent = Object.keys(devicesData.bandwidth_limits || {}).length;
            }
            
            if (suspiciousData) {
                document.getElementById('suspicious-devices').textContent = suspiciousData.suspicious_devices?.length || 0;
            }
            
            if (healthData) {
                displayNetworkHealth(healthData);
            }
        }

        function displayNetworkHealth(health) {
            const container = document.getElementById('network-health');
            container.innerHTML = `
                <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 600; color: ${health.gateway_status === 'online' ? 'hsl(var(--success))' : 'hsl(var(--destructive))'}">${health.gateway_status}</div>
                        <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground))">Gateway</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 600; color: ${health.dns_status === 'online' ? 'hsl(var(--success))' : 'hsl(var(--destructive))'}">${health.dns_status}</div>
                        <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground))">DNS</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 600;">${health.bandwidth_utilization?.toFixed(1) || 0}%</div>
                        <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground))">Bandwidth Usage</div>
                    </div>
                </div>
            `;
        }

        // Device Management Functions
        async function startScan() {
            const btn = document.getElementById('scanBtn');
            btn.disabled = true;
            btn.innerHTML = '🔍 Scanning...';
            
            document.getElementById('devices-loading').classList.remove('hidden');
            
            const result = await apiRequest('/api/scan');
            if (result) {
                updateStatus('Scanning network... This may take a few minutes.');
                scanInterval = setInterval(checkScanProgress, 2000);
            } else {
                btn.disabled = false;
                btn.innerHTML = '🔍 Scan Network';
                document.getElementById('devices-loading').classList.add('hidden');
            }
        }

        async function checkScanProgress() {
            const data = await apiRequest('/api/devices');
            if (data && !data.scanning) {
                clearInterval(scanInterval);
                document.getElementById('scanBtn').disabled = false;
                document.getElementById('scanBtn').innerHTML = '🔍 Scan Network';
                document.getElementById('devices-loading').classList.add('hidden');
                displayDevices(data);
            } else if (data) {
                displayDevices(data);
            }
        }

        async function refreshDevices() {
            const data = await apiRequest('/api/devices');
            if (data) {
                displayDevices(data);
            }
        }

        function displayDevices(data) {
            currentDevices = data.devices || {};
            const container = document.getElementById('devices-container');
            
            updateStatus(`Found ${data.total_devices || 0} online devices. Last scan: ${formatDate(data.last_scan)}`);

            if (Object.keys(currentDevices).length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 3rem; color: hsl(var(--muted-foreground));">No devices found. Try scanning again.</div>';
                return;
            }

            let html = '<div class="devices-grid">';
            
            Object.values(currentDevices).forEach(device => {
                const isBlocked = data.blocked_devices?.includes(device.ip);
                const hasLimit = data.bandwidth_limits?.[device.ip];
                
                html += `
                    <div class="card device-card ${isBlocked ? 'blocked' : ''} ${hasLimit ? 'limited' : ''}">
                        <div class="card-header">
                            <div class="device-header">
                                <div class="device-icon">${device.icon || '🔧'}</div>
                                <div class="device-title">
                                    <h3>${device.hostname || 'Unknown Device'}</h3>
                                    <div class="device-ip">${device.ip}</div>
                                </div>
                            </div>
                            <div class="device-status">
                                <span class="badge badge-success">${device.status?.toUpperCase()}</span>
                                ${isBlocked ? '<span class="badge badge-destructive">BLOCKED</span>' : ''}
                                ${hasLimit ? `<span class="badge badge-warning">LIMITED ${hasLimit}kbps</span>` : ''}
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-bottom: 1rem;">
                                ${device.mac ? `<div><strong>MAC:</strong><br><code style="font-size: 0.75rem;">${device.mac}</code></div>` : ''}
                                ${device.device_type ? `<div><strong>Type:</strong><br>${device.device_type}</div>` : ''}
                                ${device.vendor && device.vendor !== 'Unknown' ? `<div><strong>Vendor:</strong><br>${device.vendor}</div>` : ''}
                                ${device.ports?.length ? `<div><strong>Ports:</strong><br>${device.ports.length} open</div>` : ''}
                            </div>
                            
                            ${device.ports && device.ports.length > 0 ? `
                                <div style="margin-bottom: 1rem;">
                                    <div style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem;">Open Ports:</div>
                                    <div style="display: flex; flex-wrap: wrap; gap: 0.25rem;">
                                        ${device.ports.map(port => `<span class="badge">${port.port}/${port.service}</span>`).join('')}
                                    </div>
                                </div>
                            ` : ''}
                            
                            <div class="device-actions">
                                ${!isBlocked ? 
                                    `<button class="btn btn-destructive" onclick="blockDevice('${device.ip}', '${device.mac || ''}')">🚫 Block</button>` :
                                    `<button class="btn btn-success" onclick="unblockDevice('${device.ip}', '${device.mac || ''}')">✅ Unblock</button>`
                                }
                                <button class="btn btn-warning" onclick="showBandwidthModal('${device.ip}', '${device.mac || ''}')">⚡ Bandwidth</button>
                                <button class="btn btn-destructive" onclick="kickDevice('${device.ip}', '${device.mac || ''}')">👢 Kick</button>
                                <button class="btn" onclick="monitorDevice('${device.ip}')">📊 Monitor</button>
                                <button class="btn" onclick="addToWhitelist('${device.ip}', '${device.mac || ''}')">⭐ Whitelist</button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // Device Action Functions
        async function blockDevice(ip, mac) {
            const result = await apiRequest('/api/block-device', {
                method: 'POST',
                body: JSON.stringify({ip, mac})
            });
            
            if (result?.success) {
                showAlert('success', `Device ${ip} has been blocked`);
                refreshDevices();
            } else {
                showAlert('error', 'Failed to block device');
            }
        }

        async function unblockDevice(ip, mac) {
            const result = await apiRequest('/api/unblock-device', {
                method: 'POST',
                body: JSON.stringify({ip, mac})
            });
            
            if (result?.success) {
                showAlert('success', `Device ${ip} has been unblocked`);
                refreshDevices();
            } else {
                showAlert('error', 'Failed to unblock device');
            }
        }

        async function kickDevice(ip, mac) {
            if (confirm(`Are you sure you want to kick device ${ip} from the network?`)) {
                const result = await apiRequest('/api/kick-device', {
                    method: 'POST',
                    body: JSON.stringify({ip, mac})
                });
                
                if (result?.success) {
                    showAlert('success', `Device ${ip} has been kicked from network`);
                    refreshDevices();
                } else {
                    showAlert('error', 'Failed to kick device');
                }
            }
        }

        function showBandwidthModal(ip, mac) {
            document.getElementById('action-modal-title').textContent = `Set Bandwidth Limit - ${ip}`;
            document.getElementById('action-modal-body').innerHTML = `
                <div class="form-group">
                    <label class="form-label">Bandwidth Limit (kbps)</label>
                    <input type="number" id="bandwidth-limit" class="form-input" placeholder="1000" min="1">
                    <small style="color: hsl(var(--muted-foreground)); margin-top: 0.5rem; display: block;">
                        Leave empty to remove existing limit
                    </small>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="setBandwidthLimit('${ip}', '${mac}')">Set Limit</button>
                    <button class="btn btn-warning" onclick="removeBandwidthLimit('${ip}', '${mac}')">Remove Limit</button>
                    <button class="btn" onclick="hideModal('action-modal')">Cancel</button>
                </div>
            `;
            showModal('action-modal');
        }

        async function setBandwidthLimit(ip, mac) {
            const limit = document.getElementById('bandwidth-limit').value;
            if (!limit) return;
            
            const result = await apiRequest('/api/set-bandwidth', {
                method: 'POST',
                body: JSON.stringify({ip, mac, limit_kbps: parseInt(limit)})
            });
            
            if (result?.success) {
                showAlert('success', `Bandwidth limit set for ${ip}`);
                hideModal('action-modal');
                refreshDevices();
            } else {
                showAlert('error', 'Failed to set bandwidth limit');
            }
        }

        async function removeBandwidthLimit(ip, mac) {
            const result = await apiRequest('/api/remove-bandwidth', {
                method: 'POST',
                body: JSON.stringify({ip, mac})
            });
            
            if (result?.success) {
                showAlert('success', `Bandwidth limit removed for ${ip}`);
                hideModal('action-modal');
                refreshDevices();
            } else {
                showAlert('error', 'Failed to remove bandwidth limit');
            }
        }

        async function monitorDevice(ip) {
            showAlert('info', `Starting traffic monitoring for ${ip}...`);
            
            const result = await apiRequest(`/api/monitor-traffic/${ip}?duration=60`);
            if (result) {
                showAlert('success', `Traffic monitoring complete for ${ip}`);
            }
        }

        async function addToWhitelist(ip, mac) {
            const result = await apiRequest('/api/whitelist', {
                method: 'POST',
                body: JSON.stringify({ip, mac})
            });
            
            if (result?.success) {
                showAlert('success', `Device ${ip} added to whitelist`);
                refreshDevices();
            } else {
                showAlert('error', 'Failed to add to whitelist');
            }
        }

        // Security Functions
        async function refreshSecurity() {
            const [suspiciousData, whitelistData] = await Promise.all([
                apiRequest('/api/suspicious-activity'),
                apiRequest('/api/whitelist')
            ]);
            
            if (suspiciousData) {
                displaySuspiciousActivity(suspiciousData.suspicious_devices || []);
            }
            
            if (whitelistData) {
                displayWhitelist(whitelistData.whitelist || []);
            }
        }

        function displaySuspiciousActivity(suspicious) {
            const container = document.getElementById('suspicious-activity');
            
            if (suspicious.length === 0) {
                container.innerHTML = '<p style="color: hsl(var(--muted-foreground));">No suspicious activity detected.</p>';
                return;
            }
            
            let html = '<div style="space-y: 1rem;">';
            suspicious.forEach(item => {
                html += `
                    <div style="border: 1px solid hsl(var(--border)); border-radius: var(--radius); padding: 1rem; background-color: hsl(var(--destructive) / 0.05);">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${item.ip}</strong> - ${item.reason}
                                <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground));">Count: ${item.count}</div>
                            </div>
                            <button class="btn btn-destructive" onclick="applyDDoSMitigation('${item.ip}')">🛡️ Mitigate</button>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        function displayWhitelist(whitelist) {
            const container = document.getElementById('whitelist-container');
            
            if (whitelist.length === 0) {
                container.innerHTML = '<p style="color: hsl(var(--muted-foreground));">No whitelisted devices.</p>';
                return;
            }
            
            let html = '<div style="display: grid; gap: 0.5rem;">';
            whitelist.forEach(ip => {
                html += `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; border: 1px solid hsl(var(--border)); border-radius: var(--radius);">
                        <code>${ip}</code>
                        <button class="btn btn-destructive" onclick="removeFromWhitelist('${ip}')">❌ Remove</button>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        async function emergencyLockdown() {
            if (confirm('⚠️ This will block ALL devices except whitelisted ones. Are you sure?')) {
                const result = await apiRequest('/api/emergency-lockdown', {method: 'POST'});
                
                if (result?.success) {
                    showAlert('warning', `Emergency lockdown activated. ${result.blocked_count} devices blocked.`);
                    refreshDevices();
                } else {
                    showAlert('error', 'Failed to activate emergency lockdown');
                }
            }
        }

        async function liftLockdown() {
            const result = await apiRequest('/api/lift-lockdown', {method: 'POST'});
            
            if (result?.success) {
                showAlert('success', `Emergency lockdown lifted. ${result.unblocked_count} devices unblocked.`);
                refreshDevices();
            } else {
                showAlert('error', 'Failed to lift emergency lockdown');
            }
        }

        async function applyDDoSMitigation(ip) {
            const result = await apiRequest('/api/ddos-mitigation', {
                method: 'POST',
                body: JSON.stringify({ip})
            });
            
            if (result?.success) {
                showAlert('success', `DDoS mitigation applied for ${ip}`);
                refreshSecurity();
            } else {
                showAlert('error', 'Failed to apply DDoS mitigation');
            }
        }

        async function removeFromWhitelist(ip) {
            const result = await apiRequest('/api/whitelist', {
                method: 'DELETE',
                body: JSON.stringify({ip})
            });
            
            if (result?.success) {
                showAlert('success', `Device ${ip} removed from whitelist`);
                refreshSecurity();
            } else {
                showAlert('error', 'Failed to remove from whitelist');
            }
        }

        // Logs Functions
        async function refreshLogs() {
            const data = await apiRequest('/api/access-logs');
            if (data) {
                displayLogs(data.logs || []);
            }
        }

        function displayLogs(logs) {
            const container = document.getElementById('logs-container');
            
            if (logs.length === 0) {
                container.innerHTML = '<p style="padding: 1rem; color: hsl(var(--muted-foreground));">No logs available.</p>';
                return;
            }
            
            let html = '';
            logs.forEach(log => {
                html += `
                    <div class="log-entry ${log.admin_action ? 'admin-action' : ''}">
                        <div>
                            <strong>${log.device_ip}</strong> - ${log.action}
                            ${log.device_mac && log.device_mac !== 'unknown' ? `<br><small>MAC: ${log.device_mac}</small>` : ''}
                        </div>
                        <div style="font-size: 0.875rem; color: hsl(var(--muted-foreground));">
                            ${formatDate(log.timestamp)}
                            ${log.admin_action ? '<span style="color: hsl(var(--primary));">👤 Admin</span>' : ''}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // Settings Functions
        async function refreshSettings() {
            const data = await apiRequest('/api/network-info');
            if (data) {
                displayNetworkInfo(data);
            }
        }

        function displayNetworkInfo(info) {
            const container = document.getElementById('network-info');
            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div>
                        <strong>Local IP:</strong><br>
                        <code>${info.local_ip}</code>
                    </div>
                    <div>
                        <strong>Network Range:</strong><br>
                        <code>${info.network_range}</code>
                    </div>
                    <div>
                        <strong>Gateway IP:</strong><br>
                        <code>${info.gateway_ip}</code>
                    </div>
                    <div>
                        <strong>Interface:</strong><br>
                        <code>${info.interface || 'N/A'}</code>
                    </div>
                </div>
            `;
        }

        // Bulk Actions
        function showBulkActions() {
            const deviceSelection = document.getElementById('device-selection');
            let html = '';
            
            Object.values(currentDevices).forEach(device => {
                html += `
                    <label style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; border: 1px solid hsl(var(--border)); border-radius: var(--radius); margin-bottom: 0.5rem;">
                        <input type="checkbox" value="${device.ip}">
                        ${device.icon || '🔧'} ${device.hostname || device.ip} (${device.ip})
                    </label>
                `;
            });
            
            deviceSelection.innerHTML = html;
            
            document.getElementById('bulk-action').addEventListener('change', function() {
                const bandwidthInput = document.getElementById('bandwidth-input');
                bandwidthInput.style.display = this.value === 'set_bandwidth' ? 'block' : 'none';
            });
            
            showModal('bulk-modal');
        }

        async function executeBulkAction() {
            const action = document.getElementById('bulk-action').value;
            const selectedIPs = Array.from(document.querySelectorAll('#device-selection input:checked')).map(cb => cb.value);
            const bandwidthLimit = document.getElementById('bulk-bandwidth').value;
            
            if (selectedIPs.length === 0) {
                showAlert('warning', 'Please select at least one device');
                return;
            }
            
            const requestBody = {
                action,
                device_ips: selectedIPs
            };
            
            if (action === 'set_bandwidth' && bandwidthLimit) {
                requestBody.bandwidth_limit = parseInt(bandwidthLimit);
            }
            
            const result = await apiRequest('/api/bulk-action', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });
            
            if (result) {
                const successful = result.results.filter(r => r.success).length;
                showAlert('success', `Bulk action completed: ${successful}/${selectedIPs.length} devices processed successfully`);
                hideModal('bulk-modal');
                refreshDevices();
            } else {
                showAlert('error', 'Bulk action failed');
            }
        }

        // Configuration Management
        async function exportConfig() {
            const config = await apiRequest('/api/export-config');
            if (config) {
                const blob = new Blob([JSON.stringify(config, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `network-config-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showAlert('success', 'Configuration exported successfully');
            }
        }

        function importConfig() {
            const file = document.getElementById('config-file').files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const config = JSON.parse(e.target.result);
                    const result = await apiRequest('/api/import-config', {
                        method: 'POST',
                        body: JSON.stringify(config)
                    });
                    
                    if (result?.success) {
                        showAlert('success', 'Configuration imported successfully');
                        refreshDevices();
                    } else {
                        showAlert('error', 'Failed to import configuration');
                    }
                } catch (error) {
                    showAlert('error', 'Invalid configuration file');
                }
            };
            reader.readAsText(file);
        }

        async function backupDatabase() {
            const backup = await apiRequest('/api/backup-restore');
            if (backup) {
                const blob = new Blob([JSON.stringify(backup, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `database-backup-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showAlert('success', 'Database backup created successfully');
            }
        }

        // Utility Functions
        function refreshAllData() {
            refreshDashboard();
            refreshDevices();
            refreshSecurity();
            refreshSettings();
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function formatDate(dateString) {
            if (!dateString) return 'Never';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        function showModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.getElementById(modalId).style.display = 'flex';
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.getElementById(modalId).style.display = 'none';
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.classList.remove('hidden');
            setTimeout(() => element.classList.add('hidden'), 5000);
        }

        function showAlert(type, message) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '1rem';
            alert.style.right = '1rem';
            alert.style.zIndex = '1001';
            alert.style.maxWidth = '400px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function toggleTheme() {
            isDarkMode = !isDarkMode;
            const body = document.body;
            const toggle = document.getElementById('theme-toggle');
            
            if (isDarkMode) {
                body.classList.add('dark');
                toggle.textContent = '☀️';
            } else {
                body.classList.remove('dark');
                toggle.textContent = '🌙';
            }
            
            localStorage.setItem('darkMode', isDarkMode);
        }

        // Initialize theme from localStorage
        if (localStorage.getItem('darkMode') === 'true') {
            toggleTheme();
        }

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            if (authToken) {
                refreshAllData();
            }
        }, 30000);
    </script>
</body>
</html>