#!/usr/bin/env python3
"""
LAN Device Scanner with Web Interface
Scans local network for devices and displays them in a web interface
"""

import socket
import subprocess
import threading
import time
import json
from datetime import datetime
import ipaddress
import re
import platform
from flask import Flask, render_template, jsonify
import nmap

app = Flask(__name__)

class LANScanner:
    def __init__(self):
        self.devices = {}
        self.scanning = False
        self.last_scan = None
        
    def get_local_ip(self):
        """Get the local IP address"""
        try:
            # Connect to a remote address to determine local IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "***********"  # Fallback
    
    def get_network_range(self):
        """Get the network range to scan"""
        local_ip = self.get_local_ip()
        # Assume /24 subnet
        network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)
        return str(network)
    
    def get_device_icon(self, device_info):
        """Determine device icon based on device information"""
        hostname = device_info.get('hostname', '').lower()
        mac = device_info.get('mac', '').lower()
        vendor = device_info.get('vendor', '').lower()
        
        # Router/Gateway detection
        if any(keyword in hostname for keyword in ['router', 'gateway', 'modem']):
            return '🌐'
        if any(keyword in vendor for keyword in ['cisco', 'netgear', 'linksys', 'asus', 'tp-link']):
            return '🌐'
            
        # Mobile devices
        if any(keyword in hostname for keyword in ['iphone', 'android', 'mobile', 'phone']):
            return '📱'
        if any(keyword in vendor for keyword in ['apple', 'samsung', 'huawei', 'xiaomi']):
            return '📱'
            
        # Computers
        if any(keyword in hostname for keyword in ['pc', 'desktop', 'laptop', 'macbook', 'imac']):
            return '💻'
        if any(keyword in vendor for keyword in ['dell', 'hp', 'lenovo', 'apple', 'microsoft']):
            return '💻'
            
        # Smart TV / Media devices
        if any(keyword in hostname for keyword in ['tv', 'roku', 'chromecast', 'firestick', 'appletv']):
            return '📺'
        if any(keyword in vendor for keyword in ['roku', 'google', 'amazon', 'sony', 'lg', 'samsung']):
            return '📺'
            
        # IoT / Smart devices
        if any(keyword in hostname for keyword in ['smart', 'iot', 'alexa', 'echo', 'nest']):
            return '🏠'
            
        # Printers
        if any(keyword in hostname for keyword in ['printer', 'print', 'canon', 'epson', 'hp']):
            return '🖨️'
            
        # Gaming consoles
        if any(keyword in hostname for keyword in ['xbox', 'playstation', 'nintendo', 'switch']):
            return '🎮'
            
        # Default device
        return '🔧'
    
    def get_mac_vendor(self, mac_address):
        """Get vendor information from MAC address"""
        if not mac_address:
            return "Unknown"
            
        # Simple vendor lookup (you could expand this with a full OUI database)
        mac_prefixes = {
            '00:50:56': 'VMware',
            '08:00:27': 'VirtualBox',
            '00:0c:29': 'VMware',
            '00:1b:21': 'Intel',
            '00:23:24': 'Apple',
            '28:cf:e9': 'Apple',
            '3c:07:54': 'Apple',
            'b8:27:eb': 'Raspberry Pi',
            'dc:a6:32': 'Raspberry Pi',
            '00:16:3e': 'Xen',
            '52:54:00': 'QEMU/KVM',
        }
        
        mac_prefix = mac_address[:8].lower()
        return mac_prefixes.get(mac_prefix, "Unknown")
    
    def scan_device(self, ip):
        """Scan a single device for detailed information"""
        device_info = {
            'ip': ip,
            'hostname': '',
            'mac': '',
            'vendor': '',
            'ports': [],
            'os': '',
            'status': 'offline',
            'response_time': None,
            'last_seen': datetime.now().isoformat()
        }
        
        try:
            # Use nmap for comprehensive scanning
            nm = nmap.PortScanner()
            result = nm.scan(ip, arguments='-sn -O --host-timeout 3s')
            
            if ip in result['scan']:
                host_info = result['scan'][ip]
                
                if host_info['status']['state'] == 'up':
                    device_info['status'] = 'online'
                    
                    # Get hostname
                    if 'hostnames' in host_info and host_info['hostnames']:
                        device_info['hostname'] = host_info['hostnames'][0]['name']
                    
                    # Get MAC address and vendor
                    if 'addresses' in host_info:
                        if 'mac' in host_info['addresses']:
                            device_info['mac'] = host_info['addresses']['mac']
                            device_info['vendor'] = self.get_mac_vendor(device_info['mac'])
                    
                    # Try to get hostname via reverse DNS if not found
                    if not device_info['hostname']:
                        try:
                            device_info['hostname'] = socket.gethostbyaddr(ip)[0]
                        except:
                            device_info['hostname'] = f"Device-{ip.split('.')[-1]}"
                    
                    # Get OS information if available
                    if 'osmatch' in host_info and host_info['osmatch']:
                        device_info['os'] = host_info['osmatch'][0]['name']
                    
                    # Quick port scan for common services
                    port_result = nm.scan(ip, '22,23,53,80,135,139,443,445,993,995', arguments='--host-timeout 2s')
                    if ip in port_result['scan'] and 'tcp' in port_result['scan'][ip]:
                        for port, info in port_result['scan'][ip]['tcp'].items():
                            if info['state'] == 'open':
                                device_info['ports'].append({
                                    'port': port,
                                    'service': info.get('name', 'unknown'),
                                    'product': info.get('product', '')
                                })
        
        except Exception as e:
            print(f"Error scanning {ip}: {e}")
        
        # Add device icon
        device_info['icon'] = self.get_device_icon(device_info)
        
        return device_info
    
    def scan_network(self):
        """Scan the entire network"""
        if self.scanning:
            return
            
        self.scanning = True
        print("🔍 Starting network scan...")
        
        network_range = self.get_network_range()
        print(f"📡 Scanning network: {network_range}")
        
        # Clear previous results
        self.devices = {}
        
        # Get all IPs in the network
        network = ipaddress.IPv4Network(network_range)
        
        # Use threading for faster scanning
        threads = []
        for ip in network.hosts():
            ip_str = str(ip)
            thread = threading.Thread(target=self._scan_and_store, args=(ip_str,))
            threads.append(thread)
            thread.start()
            
            # Limit concurrent threads
            if len(threads) >= 20:
                for t in threads:
                    t.join()
                threads = []
        
        # Wait for remaining threads
        for thread in threads:
            thread.join()
        
        self.last_scan = datetime.now()
        self.scanning = False
        print(f"✅ Scan complete! Found {len([d for d in self.devices.values() if d['status'] == 'online'])} online devices")
    
    def _scan_and_store(self, ip):
        """Helper method to scan and store device info"""
        device_info = self.scan_device(ip)
        if device_info['status'] == 'online':
            self.devices[ip] = device_info

# Global scanner instance
scanner = LANScanner()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/devices')
def get_devices():
    """API endpoint to get device list"""
    online_devices = {ip: info for ip, info in scanner.devices.items() if info['status'] == 'online'}
    return jsonify({
        'devices': online_devices,
        'total_devices': len(online_devices),
        'last_scan': scanner.last_scan.isoformat() if scanner.last_scan else None,
        'scanning': scanner.scanning
    })

@app.route('/api/scan')
def start_scan():
    """API endpoint to start a new scan"""
    if not scanner.scanning:
        thread = threading.Thread(target=scanner.scan_network)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'scan_started'})
    else:
        return jsonify({'status': 'already_scanning'})

if __name__ == '__main__':
    print("🚀 Starting LAN Scanner Web Server...")
    print("📡 This will scan your local network for devices")
    print("🌐 Web interface will be available at: http://localhost:5000")
    print("⚠️  Note: This requires 'python-nmap' and 'flask' packages")
    print("   Install with: pip install python-nmap flask")
    
    # Start initial scan in background
    initial_scan_thread = threading.Thread(target=scanner.scan_network)
    initial_scan_thread.daemon = True
    initial_scan_thread.start()
    
    # Start web server
    app.run(host='0.0.0.0', port=5000, debug=False)
